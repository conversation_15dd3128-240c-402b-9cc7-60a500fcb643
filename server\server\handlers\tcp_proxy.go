package handlers

import (
	"bufio"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"net/http/httputil"
	"strings"
	"sync"
	"time"

	"socks/server/application/service"
	"socks/server/domain/entity"
	"socks/server/monitor"
	"socks/server/util"
)

var (
	tcpProxyHandler *TCPProxyHandler
	tphOnce         sync.Once
)

type TCPProxyHandler struct {
	urlProxyService service.UrlProxyService
}

func GetTCPProxyHandler() *TCPProxyHandler {
	tphOnce.Do(func() {
		tcpProxyHandler = &TCPProxyHandler{
			urlProxyService: service.GetUrlProxyService(util.SystemConfig),
		}
	})
	return tcpProxyHandler
}

// DataTCPHandler 处理客户端的data tcp连接请求
func (h *TCPProxyHandler) DataTCPHandler(w http.ResponseWriter, r *http.Request) {
	// 只接受GET请求，后续会升级为TCP连接
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 获取请求参数 - 与/register接口类似的验证
	clientUUID := r.URL.Query().Get("client_uuid")
	connID := r.URL.Query().Get("conn_id")

	if clientUUID == "" {
		http.Error(w, "Missing client_uuid parameter", http.StatusBadRequest)
		return
	}

	if connID == "" {
		http.Error(w, "Missing conn_id parameter", http.StatusBadRequest)
		return
	}

	// 验证客户端是否已注册 - 与/register接口类似的验证
	tunnel := h.urlProxyService.GetTunnel(clientUUID)
	if tunnel == nil {
		http.Error(w, "Client not registered or tunnel not found", http.StatusNotFound)
		return
	}

	// 劫持HTTP连接转换为TCP连接 - 与/register接口相同的处理
	hijacker, ok := w.(http.Hijacker)
	if !ok {
		http.Error(w, "Hijacking not supported", http.StatusInternalServerError)
		return
	}

	conn, bufrw, err := hijacker.Hijack()
	if err != nil {
		http.Error(w, "Failed to hijack connection", http.StatusInternalServerError)
		return
	}

	// 发送HTTP 101状态码表示切换协议 - 与/register接口相同的响应
	response := "HTTP/1.1 101 Switching Protocols\r\n" +
		"Upgrade: tcp\r\n" +
		"Connection: Upgrade\r\n\r\n"

	if _, err := bufrw.WriteString(response); err != nil {
		conn.Close()
		log.Printf("write upgrade response fail: %v", err)
		return
	}

	if err := bufrw.Flush(); err != nil {
		conn.Close()
		log.Printf("flush buffer fail: %v", err)
		return
	}

	// 将连接添加到tunnel的subConns中
	if err := tunnel.AddSubConn(connID, conn); err != nil {
		log.Printf("Failed to add sub connection: %v", err)
		conn.Close()
		return
	}
}

// StartTCPProxyServer 启动TCP代理服务器
func (h *TCPProxyHandler) StartTCPProxyServer(port int) error {
	listener, err := net.Listen("tcp", fmt.Sprintf("0.0.0.0:%d", port))
	if err != nil {
		return fmt.Errorf("failed to start TCP proxy server on port %d: %v", port, err)
	}

	go func() {
		defer listener.Close()
		for {
			conn, err := listener.Accept()
			if err != nil {
				log.Printf("TCP proxy server accept connection fail: %v", err)
				continue
			}
			// 为每个连接启动处理协程
			go h.handleTCPConnection(conn)
			// go h.waitForEOFAndClose(conn)
		}
	}()

	return nil
}

// handleTCPConnection 处理TCP连接
func (h *TCPProxyHandler) handleTCPConnection(clientConn net.Conn) {
	defer clientConn.Close()

	reader := bufio.NewReader(clientConn)
	// 1. 从 TCP 流中读取并解析完整的 HTTP 请求
	// ReadRequest 会处理好请求头、请求体的边界问题（如 Content-Length 或分块编码）
	req, err := http.ReadRequest(reader)
	if err != nil {
		// 如果连接在读取任何数据前就关闭了，这是正常情况，无需报错
		if err != io.EOF {
			log.Printf("Failed to read/parse HTTP request for conn %s", clientConn.RemoteAddr())
		}
		return
	}

	// 查找映射并构建目标路径
	mapping, baseURL, targetPath, servicePort, err := h.lookupMappingAndBuildPath(req.RequestURI)
	if err != nil {
		h.sendErrorResponse(clientConn, 404, "Not Found")
		return
	}

	// 检查映射是否在线
	if !mapping.Online {
		h.sendErrorResponse(clientConn, 503, "Service Unavailable")
		return
	}

	// 获取客户端的SafeConn连接
	tunnel := h.urlProxyService.GetTunnel(mapping.Client.UUID)
	if tunnel == nil {
		h.sendErrorResponse(clientConn, 503, "Service Unavailable")
		return
	}

	// 2. 修改请求的路由和 Host 属性
	// 直接修改解析后的请求对象的字段，比字符串替换更安全、更精确
	req.RequestURI = targetPath
	req.Host = fmt.Sprintf("localhost:%d", servicePort)
	// 清理 URL 字段，让 DumpRequest 基于 RequestURI 和 Host 重建请求行
	req.URL.Path = targetPath
	req.URL.Scheme = ""
	req.URL.Host = ""

	// 3. 将修改后的请求对象序列化回字节流
	// 第二个参数 'true' 表示也包含请求体
	modifiedReqBytes, err := httputil.DumpRequest(req, true)
	if err != nil {
		log.Printf("Failed to dump modified request for conn %s", clientConn.RemoteAddr())
		return
	}

	// 记录HTTP请求到流量监控系统
	trafficMonitor := monitor.GetGlobalMonitor()
	trafficMonitor.RecordHTTPRequest(mapping.Client.UUID, baseURL)

	// 处理TCP连接转发
	h.handleTCPForward(clientConn, modifiedReqBytes, tunnel, baseURL, mapping.Name)
}

// handleTCPForward 处理TCP连接转发
func (h *TCPProxyHandler) handleTCPForward(clientConn net.Conn, reqBytes []byte, tunnel *entity.SafeConn, baseURL, appName string) {
	// 生成唯一的连接ID
	connID := fmt.Sprintf("tcp_%d_%s", time.Now().UnixNano(), clientConn.RemoteAddr().String())

	// 发送tcp_open消息给客户端
	tcpOpenMsg := entity.ConnMessage{
		ID:   connID,
		Type: "tcp_open",
		Data: []byte(fmt.Sprintf("%s|%s", appName, baseURL)),
	}

	if err := tunnel.WriteJSON(tcpOpenMsg); err != nil {
		log.Printf("Failed to send tcp_open message: %v", err)
		h.sendErrorResponse(clientConn, 500, "Internal Server Error")
		return
	}

	// 等待客户端建立data tcp通道
	// 使用循环等待机制，最多等待5秒
	var dataConn net.Conn
	for i := 0; i < 50; i++ {
		dataConn = tunnel.GetSubConn(connID)
		if dataConn != nil {
			break
		}
		time.Sleep(100 * time.Millisecond)
	}

	if dataConn == nil {
		log.Printf("Timeout waiting for data tcp connection: %s", connID)
		h.sendErrorResponse(clientConn, 500, "Internal Server Error")
		return
	}

	// 将修改过后的请求直接写入dataConn隧道，然后将data tcp通道的响应转发回客户端
	dataConn.Write(reqBytes)
	if flusher, ok := dataConn.(interface{ Flush() error }); ok {
		flusher.Flush()
	}

	// 将data tcp通道的响应转发回客户端
	io.Copy(clientConn, dataConn)

	// 清理连接
	tunnel.DeleteSubConn(connID)
}

// sendErrorResponse 发送错误响应
func (h *TCPProxyHandler) sendErrorResponse(conn net.Conn, statusCode int, statusText string) {
	response := fmt.Sprintf("HTTP/1.1 %d %s\r\nContent-Type: text/plain\r\nContent-Length: %d\r\n\r\n%s",
		statusCode, statusText, len(statusText), statusText)
	conn.Write([]byte(response))
}

// lookupMappingAndBuildPath 查找映射并构建目标路径（复用URLProxyHandler的逻辑）
func (h *TCPProxyHandler) lookupMappingAndBuildPath(urlPath string) (*entity.URLMapping, string, string, int, error) {
	urlParts := strings.Split(strings.Trim(urlPath, "/"), "/")
	if len(urlParts) < 4 {
		return nil, "", "", 0, fmt.Errorf("invalid URL path")
	}

	var mapping *entity.URLMapping
	var targetPath string

	// 根据客户端类型查找匹配的映射
	switch strings.ToUpper(urlParts[1]) {
	case "AGENT":
		if len(urlParts) >= 4 {
			// Agent路径格式: /Tunnel/Agent/客户端名称/服务名称/...
			urlPath := fmt.Sprintf("/%s/%s/%s/%s", urlParts[0], urlParts[1], urlParts[2], urlParts[3])
			mapping = h.urlProxyService.GetURLMapping(urlPath)
			if len(urlParts) > 4 {
				targetPath = "/" + strings.Join(urlParts[4:], "/")
			} else {
				targetPath = "/"
			}
		}
	case "AI", "API", "ALG":
		if len(urlParts) >= 5 {
			// AI/API/ALG路径格式: /(AI|API)/服务组别/服务名称/客户端名称/...
			urlPath := fmt.Sprintf("/%s/%s/%s/%s/%s", urlParts[0], urlParts[1], urlParts[2], urlParts[3], urlParts[4])
			mapping = h.urlProxyService.GetURLMapping(urlPath)
			if len(urlParts) > 5 {
				targetPath = "/" + strings.Join(urlParts[5:], "/")
			} else {
				targetPath = "/"
			}
		}
	default:
		return nil, "", "", 0, fmt.Errorf("unsupported API type: %s", urlParts[1])
	}

	if mapping == nil {
		return nil, "", "", 0, fmt.Errorf("no mapping found for path: %s", urlPath)
	}

	// 获取最长匹配的baseURL
	baseUrl, servicePort := mapping.MatchBaseURL(targetPath)

	return mapping, baseUrl, targetPath, servicePort, nil
}
