package dto

import "socks/server/domain/entity"

// ClientFilterRequest 客户端筛选请求
type ClientFilterRequest struct {
	Name  string `json:"name,omitempty"`  // 客户端名称，空字符串表示不筛选此字段
	IP    string `json:"ip,omitempty"`    // 客户端IP地址，空字符串表示不筛选此字段
	UUID  string `json:"uuid,omitempty"`  // 客户端唯一id，空字符串表示不筛选此字段
	Type  string `json:"type,omitempty"`  // 端口对应的代理，空字符串表示不筛选此字段
	Group string `json:"group,omitempty"` // 代理客户端所在集群组别，空字符串表示不筛选此字段
}

// ClientFilterResponse 客户端筛选响应
type ClientFilterResponse struct {
	Success bool         `json:"success"`
	Message string       `json:"message,omitempty"`
	Data    []ClientInfo `json:"data,omitempty"`
	Total   int          `json:"total"`
}

// ClientInfo 客户端信息
type ClientInfo struct {
	Name  string `json:"name"`  // 客户端名称
	IP    string `json:"ip"`    // 客户端IP地址
	UUID  string `json:"uuid"`  // 客户端唯一id
	Type  string `json:"type"`  // 端口对应的代理
	Group string `json:"group"` // 代理客户端所在集群组别
}

// ToClientInfo 将 entity.Client 转换为 ClientInfo
func ToClientInfo(client *entity.Client) ClientInfo {
	return ClientInfo{
		Name:  client.Name,
		IP:    client.IP,
		UUID:  client.UUID,
		Type:  client.Type,
		Group: client.Group,
	}
}

// ToClientInfoList 将 entity.Client 列表转换为 ClientInfo 列表
func ToClientInfoList(clients []*entity.Client) []ClientInfo {
	result := make([]ClientInfo, len(clients))
	for i, client := range clients {
		result[i] = ToClientInfo(client)
	}
	return result
}

// ClientGroupResponse 按组分组的客户端响应
type ClientGroupResponse struct {
	Success bool                    `json:"success"`
	Message string                  `json:"message,omitempty"`
	Data    map[string][]ClientInfo `json:"data,omitempty"`
	Total   int                     `json:"total"`
}

// GroupInfo 组信息
type GroupInfo struct {
	GroupName string       `json:"group_name"`
	Clients   []ClientInfo `json:"clients"`
	Count     int          `json:"count"`
}

// ClientGroupDetailResponse 详细的按组分组响应（包含每组的统计信息）
type ClientGroupDetailResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    []GroupInfo `json:"data,omitempty"`
	Total   int         `json:"total"`
}

// ToClientGroupResponse 将按组分组的客户端数据转换为响应格式
func ToClientGroupResponse(clientsByGroup map[string][]*entity.Client) ClientGroupResponse {
	data := make(map[string][]ClientInfo)
	total := 0

	for group, clients := range clientsByGroup {
		data[group] = ToClientInfoList(clients)
		total += len(clients)
	}

	return ClientGroupResponse{
		Success: true,
		Data:    data,
		Total:   total,
	}
}

// ToClientGroupDetailResponse 将按组分组的客户端数据转换为详细响应格式
func ToClientGroupDetailResponse(clientsByGroup map[string][]*entity.Client) ClientGroupDetailResponse {
	var data []GroupInfo
	total := 0

	for group, clients := range clientsByGroup {
		groupInfo := GroupInfo{
			GroupName: group,
			Clients:   ToClientInfoList(clients),
			Count:     len(clients),
		}
		data = append(data, groupInfo)
		total += len(clients)
	}

	return ClientGroupDetailResponse{
		Success: true,
		Data:    data,
		Total:   total,
	}
}
