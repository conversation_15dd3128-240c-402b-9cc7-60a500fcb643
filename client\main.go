package main

import (
	"bufio"
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	util "socks/client/util"
)

// Message format for TCP communication
type Message struct {
	ID   string `json:"id"`
	Type string `json:"type"`
	Data []byte `json:"data,omitempty"`
}

// URLProxyMessage URL代理消息结构
type URLProxyMessage struct {
	ID        string            `json:"id"`                   // 请求 ID
	Type      string            `json:"type"`                 // proxy_request, proxy_response
	BaseURL   string            `json:"base_url,omitempty"`   // URL路径
	TargetURL string            `json:"target_url,omitempty"` // 目标URL
	Method    string            `json:"method,omitempty"`     // HTTP方法
	Headers   map[string]string `json:"headers,omitempty"`    // HTTP头
	Body      []byte            `json:"body,omitempty"`       // 请求/响应体
	Status    int               `json:"status,omitempty"`     // HTTP状态码（响应时使用）
	Error     string            `json:"error,omitempty"`      // 错误信息
}

// SafeConn now wraps a TCP connection instead of WebSocket
type SafeConn struct {
	Conn net.Conn
	wmu  sync.Mutex
	enc  *json.Encoder
	dec  *json.Decoder
	bw   *bufio.Writer // 添加缓冲写入器
}

func (c *SafeConn) WriteJSON(v interface{}) error {
	c.wmu.Lock()
	defer c.wmu.Unlock()
	err := c.enc.Encode(v)
	if err == nil && c.bw != nil {
		err = c.bw.Flush()
	}
	if bw, ok := c.Conn.(interface{ Flush() error }); ok {
		bw.Flush()
	}
	return err
}

func (c *SafeConn) ReadJSON(v interface{}) error {
	return c.dec.Decode(v)
}

func (c *SafeConn) Close() error {
	return c.Conn.Close()
}

// Config 配置信息
type Config struct {
	TunnelClientConfig
	UUID string
}

// ConnectionManager 连接管理器
type ConnectionManager struct {
	conns map[string]net.Conn
	mu    sync.Mutex
}

type UrlMapping struct {
	Host string
	Port int
}

// PortMapping updated to use TCP connection
type PortMapping struct {
	LocalPort  int                // Local port
	RemotePort int                // Remote port
	Conn       *SafeConn          // TCP connection
	CM         *ConnectionManager // Connection manager
	Created    time.Time          // Creation time
}

type URLRegisterRequest struct {
	ApiType      string `json:"api_type"`      // Agent, AI, or API
	ServiceGroup string `json:"service_group"` // 服务组别
	ServiceName  string `json:"service_name"`  // 服务名称
	ServicePort  int    `json:"service_port"`  // 服务端口
	AppName      string `json:"app_name"`      // 应用名称
	ClientUUID   string `json:"client_id"`     // 客户端UUID
	BaseURL      string `json:"base_url"`      // 基础URL，客户端服务的基础路径
}

type URLRegisterResponse struct {
	Success bool   `json:"success"`
	URLPath string `json:"url_path"`
}

// 全局映射管理
var (
	// 存储所有活跃的端口映射
	portMappings    = make(map[int]*PortMapping) // 本地端口 -> 映射信息
	portMappingsMux sync.Mutex

	urlMappings    = make(map[string]map[string]*UrlMapping) // [AppName][baseURL]*UrlMapping
	urlMappingsMux sync.Mutex
)

// 全局控制连接
var (
	globalConn *SafeConn

	// 全局连接管理器
	globalCM *ConnectionManager

	globalPortMappings    = make(map[int]*PortMapping) // 服务端口 -> 映射信息
	globalPortMappingsMux sync.RWMutex
)

// NewConnectionManager 创建连接管理器
func NewConnectionManager() *ConnectionManager {
	return &ConnectionManager{
		conns: make(map[string]net.Conn),
	}
}

// Add 添加连接
func (cm *ConnectionManager) Add(id string, conn net.Conn) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.conns[id] = conn
}

// Remove 移除连接
func (cm *ConnectionManager) Remove(id string) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	if conn, ok := cm.conns[id]; ok {
		conn.Close()
		delete(cm.conns, id)
	}
}

// Get 获取连接
func (cm *ConnectionManager) Get(id string) net.Conn {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	return cm.conns[id]
}

// parseFlags 解析命令行参数
func parseFlags() (*Config, error) {
	var configPath string
	config := &Config{
		UUID: util.GenerateUUID(),
	}

	flag.StringVar(&configPath, "config", "", "配置文件路径")
	flag.StringVar(&config.ServerIP, "server", config.ServerIP, "中转服务器IP")
	flag.StringVar(&config.LocalHost, "host", config.LocalHost, "本地服务IP")
	flag.StringVar(&config.Type, "type", config.Type, "代理客户端类型")
	flag.StringVar(&config.Group, "group", config.Group, "代理客户端组别")
	flag.StringVar(&config.HostName, "name", config.HostName, "代理客户端名称")
	flag.IntVar(&config.ServerPort, "port", config.ServerPort, "中转服务器端口")
	flag.IntVar(&config.APIPort, "manager", config.APIPort, "代理客户端API服务端口")
	flag.Parse()

	if config.ServerIP == "" || config.ServerPort == 0 {
		config.TunnelClientConfig = GetTunnelConfig(configPath)
	}

	return config, nil
}

// registerURLMapping 向服务器注册URL映射
func registerURLMapping(config *Config, baseURL, appName, serviceName, serviceGroup, apiType string, servicePort int) (*URLRegisterResponse, error) {
	// 构建请求体

	requestBody := URLRegisterRequest{
		ApiType:      apiType,
		ServiceGroup: serviceGroup,
		ServiceName:  serviceName,
		ServicePort:  servicePort,
		AppName:      appName,
		ClientUUID:   config.UUID,
		BaseURL:      baseURL,
	}

	requestData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("json encode failed: %v", err)
	}

	// 构建请求URL
	url := fmt.Sprintf("http://%s:%d/url/register",
		config.ServerIP, config.ServerPort)

	// 发送POST请求
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(requestData))
	if err != nil {
		return nil, fmt.Errorf("register url mapping failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("register url mapping failed: %s", string(body))
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read server response body failed: %w", err)
	}
	// 解析响应体
	var response *URLRegisterResponse
	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		// JSON parse failed
		return nil, fmt.Errorf("failed to parse JSON response: %w", err)
	}

	log.Printf("URL mapping registered successfully: %s", response.URLPath)
	return response, nil
}

// unregisterURLMapping 向服务器取消注册URL映射
func unregisterURLMapping(config *Config, urlPath string) error {
	// Build request URL
	url := fmt.Sprintf("http://%s:%d/url/unregister?client_id=%s&url_path=%s",
		config.ServerIP, config.ServerPort, config.UUID, urlPath)

	req, err := http.NewRequest("DELETE", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create unregister request: %v", err)
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to unregister URL mapping: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to unregister URL mapping: %s", string(body))
	}

	log.Printf("URL mapping unregistered successfully: %s", urlPath)
	return nil
}

func register(config *Config) (*SafeConn, error) {
	// 获取本机IP地址
	localIP, err := util.GetLocalIP()
	if err != nil {
		log.Printf("Failed to get local IP: %v, using default IP", err)
	}

	// 构建请求URL
	serverAddr := fmt.Sprintf("%s:%d", config.ServerIP, config.ServerPort)
	url := fmt.Sprintf("http://%s/register?name=%s&type=%s&id=%s&ip=%s&group=%s",
		serverAddr, config.HostName, config.Type, config.UUID, localIP, config.Group)

	log.Printf("Connecting to server: %s", url)

	// 创建HTTP请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %v", err)
	}

	// 创建TCP连接
	conn, err := net.Dial("tcp", serverAddr)
	if err != nil {
		return nil, fmt.Errorf("connect failed: %v", err)
	}

	// 发送HTTP请求
	err = req.Write(conn)
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("send HTTP request failed: %v", err)
	}

	// 读取HTTP响应头
	resp, err := http.ReadResponse(bufio.NewReader(conn), req)
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("read response failed: %v", err)
	}
	log.Printf("sever response: %v", resp)
	// 检查响应状态
	if resp.StatusCode != http.StatusSwitchingProtocols {
		body, _ := io.ReadAll(resp.Body)
		conn.Close()
		return nil, fmt.Errorf("unexpected status code: %s, response body: %s", resp.Status, string(body))
	}

	// 设置TCP连接参数
	tcpConn := conn.(*net.TCPConn)
	tcpConn.SetKeepAlive(true)
	tcpConn.SetKeepAlivePeriod(30 * time.Second)
	tcpConn.SetWriteBuffer(32 * 1024)
	tcpConn.SetReadBuffer(32 * 1024)
	// 创建安全连接，使用更大的缓冲区
	bw := bufio.NewWriterSize(conn, 32*1024)
	safeConn := &SafeConn{
		Conn: conn,
		bw:   bw,
		enc:  json.NewEncoder(bw),
		dec:  json.NewDecoder(bufio.NewReaderSize(conn, 32*1024)),
	}

	buf := make([]byte, 1024)
	conn.SetReadDeadline(time.Now().Add(5 * time.Second)) // 设置5秒超时
	_, err = conn.Read(buf)
	if err != nil {
		log.Printf("read register confirm message failed: %v", err)
	}
	conn.SetReadDeadline(time.Time{}) // 清除超时设置
	log.Printf("successfully registered client and established control connection")

	return safeConn, nil
}

func buildTunnel(config *Config, localPort int, serviceName string) (int, error) {
	// 获取本机IP地址
	localIP, err := util.GetLocalIP()
	if err != nil {
		log.Printf("Failed to get local IP: %v, using default IP", err)
	}

	// 构建请求URL，包含本机IP和服务名称
	url := fmt.Sprintf("http://%s:%d/allocate?id=%s&port=%d&service_name=%s",
		config.ServerIP, config.ServerPort, config.UUID, localPort, serviceName)

	resp, err := http.Get(url)
	if err != nil {
		return 0, fmt.Errorf("allocate port failed: %v", err)
	}
	defer resp.Body.Close()

	// 解析服务端返回的端口信息
	var result struct {
		Port int `json:"port"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return 0, fmt.Errorf("parse response failed: %v", err)
	}

	log.Printf("successfully allocated port mapping: local port %d -> server port %d (local ip: %s)",
		localPort, result.Port, localIP)

	return result.Port, nil
}

// handleLocalToWS 处理本地到WS的数据转发
func handleLocalToWS(id string, localConn net.Conn, conn *SafeConn) {
	buf := make([]byte, 4096)
	for {
		n, err := localConn.Read(buf)

		if n > 0 {
			conn.WriteJSON(Message{ID: id, Type: "data", Data: buf[:n]})
		}
		if err != nil {
			log.Printf("read from local conn error: %v", err)
			break
		}
	}
	conn.WriteJSON(Message{ID: id, Type: "close"})
}

// handleOpenMessage 处理打开连接消息
func handleOpenMessage(msg Message, conn *SafeConn, cm *ConnectionManager, localAddr string) {
	localConn, err := net.Dial("tcp", localAddr)
	if err != nil {
		log.Printf("Failed to connect to local service: %v", err)
		conn.WriteJSON(Message{ID: msg.ID, Type: "close"})
		return
	}
	log.Printf("msg.ID: %s", msg.ID)
	cm.Add(msg.ID, localConn)
	log.Printf("Add local conn success")

	go handleLocalToWS(msg.ID, localConn, conn)
}

// handleTCPOpenMessage 处理TCP代理连接打开消息
func handleTCPOpenMessage(msg Message, config *Config) {
	// 解析baseURL和targetPath
	data := string(msg.Data)
	parts := strings.Split(data, "|")
	if len(parts) != 2 {
		log.Printf("Invalid tcp_open message data: %s", data)
		return
	}

	appName := parts[0]
	baseURL := parts[1]

	log.Printf("Handling TCP proxy connection: appName=%s, baseURL=%s", appName, baseURL)

	serverAddr := fmt.Sprintf("%s:%d", config.ServerIP, config.ServerPort)
	// 向服务器的manager port发送HTTP请求建立data tcp连接，并获取data tcp连接的TCP连接对象conn。conn是net.Conn接口的实现，可以用于与本地服务建立连接。
	url := fmt.Sprintf("http://%s/tcp/data?client_uuid=%s&conn_id=%s&ip=%s", serverAddr, config.UUID, msg.ID, config.LocalHost)
	req, err := http.NewRequest("GET", url, nil) // 创建HTTP请求对象req，用于发送HTTP请求。
	if err != nil {
		log.Printf("Failed to create request: %v", err) // 处理错误。
		return
	}

	// 创建TCP连接。
	dataTCPConn, err := net.Dial("tcp", serverAddr)
	if err != nil {
		log.Printf("connect failed: %v", err)
		return
	}

	// 发送HTTP请求
	err = req.Write(dataTCPConn)
	if err != nil {
		dataTCPConn.Close()
		log.Printf("send HTTP request failed: %v", err)
		return
	}

	// 读取HTTP响应头
	resp, err := http.ReadResponse(bufio.NewReader(dataTCPConn), req)
	if err != nil {
		dataTCPConn.Close()
		log.Printf("read response failed: %v", err)
		return
	}

	// 检查响应状态
	if resp.StatusCode != http.StatusSwitchingProtocols {
		body, _ := io.ReadAll(resp.Body)
		dataTCPConn.Close()
		log.Printf("unexpected status code: %s, response body: %s", resp.Status, string(body))
		return
	}

	if tcpConn, ok := dataTCPConn.(*net.TCPConn); ok {
		tcpConn.SetNoDelay(true)
	}

	// 查找URL映射
	var mapping *UrlMapping
	var hasMappingConfig bool
	urlMappingsMux.Lock()
	if baseUrlMapping, ok := urlMappings[appName]; ok {
		mapping, hasMappingConfig = baseUrlMapping[baseURL]
	}
	urlMappingsMux.Unlock()

	if !hasMappingConfig {
		log.Printf("No URL mapping config found for: %s", baseURL)
		dataTCPConn.Close()
		return
	}

	targetAddr := fmt.Sprintf("localhost:%d", mapping.Port)
	localConn, err := net.Dial("tcp", targetAddr)
	if err != nil {
		log.Printf("Failed to connect to local service: %s, %v", targetAddr, err)
		dataTCPConn.Close()
		return
	}

	// 启动数据转发
	go handleTCPDataForward(dataTCPConn, localConn)
}

// handleTCPDataForward 处理TCP数据转发
func handleTCPDataForward(dataTCPConn, localConn net.Conn) {
	defer func() {
		dataTCPConn.Close()
		localConn.Close()
	}()

	reqReader := bufio.NewReader(dataTCPConn)
	req, err := http.ReadRequest(reqReader)
	if err != nil {
		log.Printf("failed to read request: %v", err)
		return
	}
	// 2. 写请求到 localConn
	err = req.Write(localConn)
	if err != nil {
		log.Printf("failed to write request: %v", err)
		return
	}
	forwardOneHTTPResponse(localConn, dataTCPConn)
}

func forwardOneHTTPResponse(localConn, dataTCPConn net.Conn) {

	reader := bufio.NewReader(localConn)

	// 读取 HTTP 响应头
	resp, err := http.ReadResponse(reader, nil)
	if err != nil {
		log.Printf("failed to read response: %v", err)
		return
	}
	defer resp.Body.Close()

	// 把响应头写到 dataTCPConn
	err = resp.Write(dataTCPConn)
	if err != nil {
		log.Printf("failed to write response header: %v", err)
		return
	}
}

// handleDataMessage 处理数据消息
func handleDataMessage(msg Message, cm *ConnectionManager) {
	if localConn := cm.Get(msg.ID); localConn != nil {
		// 检查是否是URL代理连接
		localConn.Write(msg.Data)
	}
}

// 重连函数 - 一次性调用，直至重连完成才返回
func reconnectUntilSuccess(config *Config) {
	log.Printf("connection lost, starting reconnect mechanism...")

	for {
		log.Printf("Trying to reconnect to server...")
		newConn, err := register(config)
		if err != nil {
			log.Printf("Reconnect failed: %v, will retry in 30 seconds", err)
			time.Sleep(30 * time.Second)
			continue
		}

		// 重连成功，更新全局连接
		log.Printf("Reconnect succeeded, updating global connection and connection manager")

		// 关闭旧连接（如果存在）
		if globalConn != nil {
			globalConn.Close()
		}

		// 更新全局连接
		globalConn = newConn

		// 关闭旧连接管理器中的所有tcp连接
		if globalCM != nil {
			globalCM.mu.Lock()
			for id, conn := range globalCM.conns {
				conn.Close()
				delete(globalCM.conns, id)
			}
			globalCM.mu.Unlock()
		}

		// 重新启动连接管理
		buildGlobalCM(config)

		log.Printf("Reconnect complete, service is back to normal")
		return // 重连成功，退出函数
	}
}

// 获取或创建全局控制连接
func buildGlobalCM(config *Config) {
	if globalCM == nil {
		globalCM = NewConnectionManager()
	}

	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for range ticker.C {
			err := globalConn.WriteJSON(Message{
				ID:   util.HEARTBEAT,
				Type: "ping",
				Data: []byte("ping"),
			})
			if err != nil {
				log.Printf("Failed to send heartbeat: %v", err)
				return
			}
		}
	}()

	// 启动全局消息处理
	go func() {
		for {
			var msg Message
			if err := globalConn.ReadJSON(&msg); err != nil {
				log.Printf("Global connection read error: %v", err)
				// 启动重连，这个函数会阻塞直到重连成功
				go reconnectUntilSuccess(config)
				break
			}

			log.Printf("Received message: Type=%s, ID=%s", msg.Type, msg.ID)
			// Route to handler by message type and ID

			switch msg.Type {
			case "open":
				serverPort, err := util.GetServerPortByMsgId(msg.ID)
				if err != nil {
					log.Printf("Message process error: %v", err)
				}
				// 查找对应的本地端口
				globalPortMappingsMux.RLock()
				defer globalPortMappingsMux.RUnlock()
				var localAddr string
				if mapping, ok := globalPortMappings[serverPort]; ok {
					localAddr = fmt.Sprintf("0.0.0.0:%d", mapping.LocalPort)
					handleOpenMessage(msg, globalConn, globalCM, localAddr)
				} else {
					log.Printf("Server port mapping not built, server port: %d", serverPort)
				}
			case "tcp_open":
				// 处理TCP代理连接打开请求
				handleTCPOpenMessage(msg, config)
			case "data":
				handleDataMessage(msg, globalCM)

			case "close":
				globalCM.Remove(msg.ID)
			case "ping":
				// 处理心跳消息
				globalConn.WriteJSON(Message{ID: msg.ID, Type: "pong"})
			}
		}
		log.Print("message process goroutine exited, connection lost")
	}()
}

// buildPortMapping 新的端口映射逻辑，复用全局控制连接和连接管理器
func buildPortMapping(config *Config, localPort int, serviceName string) (*PortMapping, error) {
	// 检查全局连接是否可用
	if globalConn == nil {
		return nil, fmt.Errorf("global connection not built, please wait for reconnect to complete")
	}

	// Check if mapping already exists
	portMappingsMux.Lock()
	defer portMappingsMux.Unlock()
	if mapping, exists := portMappings[localPort]; exists {
		return mapping, nil
	}

	// 申请远程端口
	remotePort, err := buildTunnel(config, localPort, serviceName)
	if err != nil {
		return nil, fmt.Errorf("failed to apply for remote port: %v", err)
	}
	log.Printf("Successfully applied for port mapping: local port %d -> remote port %d", localPort, remotePort)

	// 创建映射记录，使用全局连接管理器
	mapping := &PortMapping{
		LocalPort:  localPort,
		RemotePort: remotePort,
		Conn:       globalConn, // 复用全局连接
		CM:         globalCM,   // 复用全局连接管理器
		Created:    time.Now(),
	}

	// 保存映射
	globalPortMappingsMux.Lock()
	globalPortMappings[remotePort] = mapping
	globalPortMappingsMux.Unlock()

	return mapping, nil
}

// startAPIServer 启动API服务器
func startAPIServer(config *Config) {

	// 状态端点
	http.HandleFunc("/status", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodGet {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		type MappingInfo struct {
			LocalPort  int       `json:"local_port"`
			RemotePort int       `json:"remote_port"`
			Created    time.Time `json:"created"`
		}

		var mappings []MappingInfo

		portMappingsMux.Lock()
		for _, mapping := range portMappings {
			mappings = append(mappings, MappingInfo{
				LocalPort:  mapping.LocalPort,
				RemotePort: mapping.RemotePort,
				Created:    mapping.Created,
			})
		}
		portMappingsMux.Unlock()

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"server":   fmt.Sprintf("%s:%s", config.ServerIP, config.ServerPort),
			"mappings": mappings,
			"count":    len(mappings),
		})
	})

	http.HandleFunc("/tunnel", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost && r.Method != http.MethodGet {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// 获取本地端口
		portStr := r.URL.Query().Get("port")
		if portStr == "" {
			http.Error(w, "Missing port parameter", http.StatusBadRequest)
			return
		}

		localPort := 0
		if _, err := fmt.Sscanf(portStr, "%d", &localPort); err != nil || localPort <= 0 || localPort > 65535 {
			http.Error(w, "Invalid port number", http.StatusBadRequest)
			return
		}

		serviceName := r.URL.Query().Get("service_name")

		// 设置映射
		mapping, err := buildPortMapping(config, localPort, serviceName)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		// 返回映射信息
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"local_port":  mapping.LocalPort,
			"remote_port": mapping.RemotePort,
			"server":      config.ServerIP,
			"created":     mapping.Created,
		})
	})

	// URL注册端点
	http.HandleFunc("/url/register", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		type URLRegisterRequest struct {
			AppName      string `json:"app_name"`
			ServiceName  string `json:"service_name"`
			ServiceGroup string `json:"service_group"`
			ServicePort  int    `json:"service_port"`
			ApiType      string `json:"api_type"`
			BaseURL      string `json:"base_url"`
		}

		var req URLRegisterRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			http.Error(w, "illegal request body", http.StatusBadRequest)
			return
		}

		if req.AppName == "" {
			http.Error(w, "Missing name parameter", http.StatusBadRequest)
			return
		}

		if req.ServiceGroup == "" {
			http.Error(w, "Missing group parameter", http.StatusBadRequest)
			return
		}

		if req.ApiType == "" {
			http.Error(w, "Missing type parameter", http.StatusBadRequest)
			return
		}

		if req.ServicePort <= 0 || req.ServicePort > 65535 {
			http.Error(w, "Missing port parameter", http.StatusBadRequest)
			return
		}

		urlMappingsMux.Lock()
		defer urlMappingsMux.Unlock()
		if isBaseUrlRegistered(req.AppName, req.BaseURL) {
			http.Error(w, "url already registed", http.StatusBadRequest)
			return
		}

		// 向server注册URL映射
		response, err := registerURLMapping(config, req.BaseURL, req.AppName, req.ServiceName, req.ServiceGroup, util.GetFormatServiceType(req.ApiType), req.ServicePort)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		addBaseUrlMapping(req.AppName, req.BaseURL, req.ServicePort)

		// 返回成功响应
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	})

	// URL取消注册端点
	http.HandleFunc("/url/unregister", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodDelete {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// 获取参数
		baseURL := r.URL.Query().Get("url")
		if baseURL == "" {
			http.Error(w, "Missing url_path parameter", http.StatusBadRequest)
			return
		}

		// 向server取消注册URL映射
		err := unregisterURLMapping(config, baseURL)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		// 返回成功响应
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success":  true,
			"url_path": baseURL,
		})
	})

	// 启动API服务器
	apiAddr := fmt.Sprintf("%s:%d", config.LocalHost, config.APIPort)
	log.Printf("API server started at %s", apiAddr)
	if err := http.ListenAndServe(apiAddr, nil); err != nil {
		log.Fatalf("API server start failed: %v", err)
	}
}

func isBaseUrlRegistered(appName, baseURL string) bool {
	urlMappingsMux.Lock()
	defer urlMappingsMux.Unlock()
	if baseUrlMapping, ok := urlMappings[appName]; ok {
		_, ok := baseUrlMapping[baseURL]
		return ok
	}
	return false
}

func addBaseUrlMapping(appName, baseURL string, port int) {
	urlMappingsMux.Lock()
	defer urlMappingsMux.Unlock()
	if baseUrlMapping, ok := urlMappings[appName]; ok {
		baseUrlMapping[baseURL] = &UrlMapping{Host: config.LocalHost, Port: port}
	} else {
		urlMappings[appName] = map[string]*UrlMapping{baseURL: {Host: config.LocalHost, Port: port}}
	}
}

func main() {
	// 解析命令行参数
	config, err := parseFlags()
	if err != nil {
		flag.Usage()
		os.Exit(1)
	}

	log.Printf("Registering client...")
	globalConn, err = register(config)
	if err != nil {
		log.Printf("Client registration error: %v", err)
		os.Exit(1)
	}
	log.Printf("Client registered successfully, building global connection manager...")

	buildGlobalCM(config)
	log.Printf("Global connection manager built, starting API server...")

	// 加载配置文件中的隧道配置
	for _, tunnel := range config.Tunnels {
		switch tunnel.APIType {
		case "TCP", "UDP":
			_, err := buildPortMapping(config, tunnel.ServicePort, tunnel.ServiceName)
			if err != nil {
				log.Printf("Failed to build port mapping for tunnel %v: %v", tunnel, err)
			}
		default:
			_, err := registerURLMapping(config, tunnel.BaseURL, tunnel.AppName, tunnel.ServiceName, tunnel.ServiceGroup, tunnel.APIType, tunnel.ServicePort)
			if err != nil {
				log.Printf("Failed to register URL mapping for tunnel %v: %v", tunnel, err)
				continue
			}
			addBaseUrlMapping(tunnel.AppName, tunnel.BaseURL, tunnel.ServicePort)

		}
	}

	// 启动API服务器
	startAPIServer(config)

}
