package event

import (
	"fmt"
	"socks/server/domain/entity"
	"sync"
)

var (
	registerClient *RegisterClient
	rcOnce         sync.Once
)

func GetRegisterClient() *RegisterClient {
	rcOnce.Do(func() {
		registerClient = &RegisterClient{
			clients: make(map[string]*entity.Client),
		}
	})
	return registerClient
}

type RegisterClient struct {
	clients map[string]*entity.Client
	locker  sync.RWMutex
}

func (rc *RegisterClient) AddClient(client *entity.Client) error {
	rc.locker.Lock()
	defer rc.locker.Unlock()
	if _, ok := rc.clients[client.UUID]; ok {
		return fmt.Errorf("clent alread register")
	}
	rc.clients[client.UUID] = client
	return nil
}

func (rc *RegisterClient) GetClient(uuid string) *entity.Client {
	rc.locker.RLock()
	defer rc.locker.RUnlock()
	return rc.clients[uuid]
}

func (rc *RegisterClient) DeleteClient(uuid string) {
	rc.locker.Lock()
	defer rc.locker.Unlock()
	delete(rc.clients, uuid)
}

func (rc *RegisterClient) GetAllClients() map[string]*entity.Client {
	rc.locker.RLock()
	defer rc.locker.RUnlock()
	return rc.clients
}

// ClientFilter 客户端筛选条件
type ClientFilter struct {
	Name  string // 客户端名称，空字符串表示不筛选此字段
	IP    string // 客户端IP地址，空字符串表示不筛选此字段
	UUID  string // 客户端唯一id，空字符串表示不筛选此字段
	Type  string // 端口对应的代理，空字符串表示不筛选此字段
	Group string // 代理客户端所在集群组别，空字符串表示不筛选此字段
}

// FilterClients 按条件筛选查询已经注册的客户端信息
// 仅匹配查询条件中给出的字段，其余字段为任意值匹配
func (rc *RegisterClient) FilterClients(filter ClientFilter) []*entity.Client {
	rc.locker.RLock()
	defer rc.locker.RUnlock()

	var result []*entity.Client

	for _, client := range rc.clients {
		// 检查每个筛选条件，空字符串表示不筛选该字段
		if filter.Name != "" && client.Name != filter.Name {
			continue
		}
		if filter.IP != "" && client.IP != filter.IP {
			continue
		}
		if filter.UUID != "" && client.UUID != filter.UUID {
			continue
		}
		if filter.Type != "" && client.Type != filter.Type {
			continue
		}
		if filter.Group != "" && client.Group != filter.Group {
			continue
		}

		// 如果所有条件都匹配，添加到结果中
		result = append(result, client)
	}

	return result
}
