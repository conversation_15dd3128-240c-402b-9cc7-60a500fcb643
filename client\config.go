package main

import (
	"encoding/json"
	"log"
	"os"
	"path/filepath"
	"sync"
)

const (
	defaultConfigPath = "E:\\BaseOS\\Desktop\\TunnelGatewayClient\\GatewayClient.json"
)

var (
	config TunnelClientConfig
	once   sync.Once
)

// Tunnel represents the structure of each tunnel in the configuration
type Tunnel struct {
	AppName      string `json:"app_name,omitempty"`
	BaseURL      string `json:"base_url,omitempty"`
	ServiceName  string `json:"service_name"`
	ServiceGroup string `json:"service_group,omitempty"`
	ServicePort  int    `json:"service_port"`
	APIType      string `json:"api_type"`
}

// Config represents the overall structure of the configuration file
type TunnelClientConfig struct {
	ServerIP   string   `json:"Server_ip"`
	ServerPort int      `json:"Server_port"`
	LocalHost  string   `json:"Local_host"`
	APIPort    int      `json:"Manager_port"`
	Type       string   `json:"Type"`
	Group      string   `json:"Group"`
	HostName   string   `json:"Name"`
	Tunnels    []Tunnel `json:"Tunnels"`
}

// LoadTunnelConfig 从指定路径加载隧道配置
func LoadTunnelConfig(path string) (TunnelClientConfig, error) {
	var config = TunnelClientConfig{
		Tunnels: make([]Tunnel, 0),
	}
	// 读取文件内容
	data, err := os.ReadFile(path)
	if err != nil {
		return config, err
	}

	// 解析JSON

	if err := json.Unmarshal(data, &config); err != nil {
		return config, err
	}

	return config, nil
}

func GetTunnelConfig(path string) TunnelClientConfig {
	once.Do(func() {
		var err error
		if path == "" {
			_, exeDir, err := ExecInfo()
			if err == nil && exeDir != "" {
				path = filepath.Join(exeDir, "GatewayClient.json")
			} else {
				path = defaultConfigPath
			}
		}
		config, err = LoadTunnelConfig(path)
		if err != nil {
			log.Fatalf("Load tunnel config fail: %v", err)
		}
	})
	return config
}

func ExecInfo() (exePath string, exeDir string, err error) {
	exePath, err = os.Executable()
	if err != nil {
		log.Printf("Get executable path fail: %v", err)
		return "", "", err
	}
	exePath, err = filepath.Abs(exePath)
	if err != nil {
		log.Printf("Get executable Abs fail: %v", err)
		return "", "", err
	}
	exeDir = filepath.Dir(exePath)
	return exePath, exeDir, nil
}
