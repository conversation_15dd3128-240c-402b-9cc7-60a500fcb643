# 客户端筛选查询接口

## 接口描述

提供按条件筛选查询已经注册的客户端信息的功能，支持按以下字段进行筛选：
- Name: 客户端名称
- IP: 客户端IP地址  
- UUID: 客户端唯一id
- Type: 端口对应的代理类型
- Group: 代理客户端所在集群组别

仅匹配查询条件中给出的字段，其余字段为任意值匹配。

## 接口信息

- **URL**: `/clients/filter`
- **方法**: `GET` 或 `POST`
- **Content-Type**: `application/json` (POST 请求)

## 请求参数

### GET 请求

通过查询参数传递筛选条件：

```
GET /clients/filter?name=client1&group=production&type=TCP
```

支持的查询参数：
- `name`: 客户端名称（可选）
- `ip`: 客户端IP地址（可选）
- `uuid`: 客户端唯一id（可选）
- `type`: 代理类型（可选）
- `group`: 集群组别（可选）

### POST 请求

通过请求体传递筛选条件：

```json
{
  "name": "client1",
  "ip": "*************", 
  "uuid": "uuid-123",
  "type": "TCP",
  "group": "production"
}
```

## 响应格式

```json
{
  "success": true,
  "message": "",
  "data": [
    {
      "name": "client1",
      "ip": "*************",
      "uuid": "uuid-123", 
      "type": "TCP",
      "group": "production"
    }
  ],
  "total": 1
}
```

### 响应字段说明

- `success`: 请求是否成功
- `message`: 错误信息（可选）
- `data`: 客户端信息列表
- `total`: 匹配的客户端总数

## 使用示例

### 1. 查询所有客户端

```bash
curl -X GET "http://localhost:8080/clients/filter"
```

### 2. 按名称筛选

```bash
curl -X GET "http://localhost:8080/clients/filter?name=client1"
```

### 3. 按组别和类型筛选

```bash
curl -X GET "http://localhost:8080/clients/filter?group=production&type=TCP"
```

### 4. 使用 POST 请求筛选

```bash
curl -X POST "http://localhost:8080/clients/filter" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "client1",
    "group": "production"
  }'
```

### 5. 按 IP 地址筛选

```bash
curl -X GET "http://localhost:8080/clients/filter?ip=*************"
```

## 注意事项

1. 所有筛选条件都是可选的，空字符串或未提供的字段表示不筛选该字段
2. 多个条件之间是 AND 关系，即必须同时满足所有提供的条件
3. 字符串匹配是精确匹配，不支持模糊匹配
4. 如果没有提供任何筛选条件，将返回所有已注册的客户端
