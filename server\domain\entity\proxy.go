package entity

import (
	"fmt"
	"net"
	"socks/server/util"
	"strings"
	"sync"
	"time"
)

const (
	EMPTY_MAPPING_ID = -1
)

var (
	portmappingGroup *PortMappingGroup
	pmgOnce          sync.Once
	listenerGroup    *ListenerGroup
	lgOnce           sync.Once
	urlmappingGroup  *URLMappingGroup
	umgOnce          sync.Once
)

type Protocol struct {
	TCP  bool
	UDP  bool
	HTTP bool
}

type Listener struct {
	ProxyListener net.Listener
}

type ListenerGroup struct {
	Listeners map[int]*Listener
	Locker    sync.RWMutex
}

// PortMapping 存储端口映射信息
type PortMapping struct {
	ID          int    // 数据库记录id
	Name        string // 隧道名称 主机名称:代理端口
	Client      *Client
	ClientPort  int       // 本地端口
	ServerPort  int       // 公网端口
	Listener    *Listener // 监听器
	Created     time.Time // 创建时间
	Connected   time.Time // 最后一次连接时间
	Enable      bool      // 代理是否启用
	Online      bool      // 是否在线
	Encryption  bool      // 是否加密
	Password    string    // 认证密钥
	RateLimit   int       // 最大连接数
	ServiceName string    // 具体端口对应的服务名称，不可为空
	Description string    // 代理描述
	Protocol    *Protocol // 当前端口映射的协议
}

// URLMapping 存储URL路径映射信息
type URLMapping struct {
	ID          int                     // 数据库记录id
	Name        string                  // App的名字
	Client      *Client                 // 客户端信息
	URLPath     string                  // 服务端代理的客户端URL路径，单客户端、服务唯一
	BaseURL     map[string]*ServiceInfo // 服务端代理的客户端服务URL根路径，如 /user/info
	Created     time.Time               // 创建时间
	Enable      bool                    // 映射是否启用
	Online      bool                    // 是否在线
	Encryption  bool                    // 是否加密
	Password    string                  // 认证密钥
	Description string                  // 映射描述
	Protocol    *Protocol               // 当前端口映射的协议
	sync.RWMutex
}

type ServiceInfo struct {
	ServiceName  string `json:"service_name"`
	ServiceGroup string `json:"service_group"`
	ServicePort  int    `json:"service_port"`
}

type PortMappingGroup struct {
	MinPort           int
	MaxPort           int
	Timeout           int
	MaxConnection     int
	SlidingExpiration int
	Mappings          map[string]*PortMapping // 客户端uuid+端口映射一张PortMapping表，实现持久化
	ServerPortMapping map[int]string          //代理服务端端口和客户端uuid的映射关系，方便快速查询
	Locker            sync.RWMutex
}

// URLMappingGroup 管理URL映射
type URLMappingGroup struct {
	Mappings    map[string]*URLMapping         // URL路径到映射的关系，key为URLPath
	ClientPaths map[string]map[string][]string // 客户端UUID到其注册的URL路径列表的映射 [uuid][appName][]urlPath
	Locker      sync.RWMutex
}

func (p *Protocol) GetProtocolType() string {
	if p.TCP && p.UDP {
		return "TCP,UDP"
	} else if p.TCP {
		return "TCP"
	} else if p.UDP {
		return "UDP"
	} else if p.HTTP {
		return "HTTP"
	} else {
		return "未知"
	}
}

func BuildProtocol(protocolType string) *Protocol {
	p := &Protocol{}
	if strings.Contains(protocolType, "TCP") {
		p.TCP = true
	}

	if strings.Contains(protocolType, "UDP") {
		p.UDP = true
	}

	if strings.Contains(protocolType, "HTTP") {
		p.HTTP = true
	}

	if strings.Contains(protocolType, "AI") {
		p.HTTP = true
	}

	if strings.Contains(protocolType, "AGENT") {
		p.HTTP = true
	}

	if strings.Contains(protocolType, "Agent") {
		p.HTTP = true
	}

	if strings.Contains(protocolType, "API") {
		p.HTTP = true
	}

	if strings.Contains(protocolType, "ALG") {
		p.HTTP = true
	}

	return p
}

func GetListenerGroup() *ListenerGroup {
	lgOnce.Do(func() {
		listenerGroup = &ListenerGroup{
			Listeners: make(map[int]*Listener),
		}
	})
	return listenerGroup
}

func GetPortMappingGroup(config *util.TunnelConfig) *PortMappingGroup {
	pmgOnce.Do(func() {
		portmappingGroup = &PortMappingGroup{
			MinPort:           config.MinPort,
			MaxPort:           config.MaxPort,
			Timeout:           config.Timeout,
			MaxConnection:     config.MaxConnection,
			SlidingExpiration: config.SlidingExpiration,
			Mappings:          make(map[string]*PortMapping),
			ServerPortMapping: make(map[int]string),
		}
	})

	return portmappingGroup
}

func GetURLMappingGroup() *URLMappingGroup {
	umgOnce.Do(func() {
		urlmappingGroup = &URLMappingGroup{
			Mappings:    make(map[string]*URLMapping),
			ClientPaths: make(map[string]map[string][]string),
		}
	})
	return urlmappingGroup
}

func (l *Listener) BuildListener(netType string, ip string, port int) error {

	address := fmt.Sprintf("%s:%d", ip, port)

	switch netType {
	case "tcp", "tcp4", "tcp6":
		ln, err := net.Listen(netType, address)
		if err != nil {
			return fmt.Errorf("create %s listener fail: %v", netType, err)
		}
		l.ProxyListener = ln

	case "udp", "udp4", "udp6":
		// UDP 不使用 net.Listener，而是使用 net.PacketConn
		// 这里我们返回错误，因为 Listener 结构体当前设计用于 TCP
		return fmt.Errorf("udp protocol does not support net.Listener interface, please use PacketConn")

	default:
		return fmt.Errorf("unsupported network type: %s", netType)
	}

	return nil
}

func (l Listener) GetListener() net.Listener {
	return l.ProxyListener
}

func (g *ListenerGroup) AddListener(serverPort int, listener *Listener) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	g.Listeners[serverPort] = listener
}

func (g *ListenerGroup) GetListener(serverPort int) *Listener {
	g.Locker.RLock()
	defer g.Locker.RUnlock()
	if listener, ok := g.Listeners[serverPort]; ok {
		return listener
	}
	return nil
}

func (g *ListenerGroup) DeleteListener(serverPort int) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	if listener, ok := g.Listeners[serverPort]; ok {
		listener.ProxyListener.Close()
	}
	delete(g.Listeners, serverPort)
}

func (g *PortMappingGroup) AddMapping(clientUUID string, clientPort int, mapping *PortMapping) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	cacheKey := fmt.Sprintf("%s:%d", clientUUID, clientPort)
	g.Mappings[cacheKey] = mapping
	g.ServerPortMapping[mapping.ServerPort] = clientUUID
}

func (g *PortMappingGroup) GetMapping(clientUUID string, clientPort int) *PortMapping {
	g.Locker.RLock()
	defer g.Locker.RUnlock()
	cacheKey := fmt.Sprintf("%s:%d", clientUUID, clientPort)
	if mapping, ok := g.Mappings[cacheKey]; ok {
		return mapping
	}
	return nil
}

func (g *PortMappingGroup) DeleteMapping(clientUUID string, clientPort int) int {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	mapping := g.GetMapping(clientUUID, clientPort)
	if mapping == nil {
		return EMPTY_MAPPING_ID
	}
	// 必须删除ServerPortMapping这个map，通过它来判断端口是否已经分配
	delete(g.ServerPortMapping, mapping.ServerPort)
	cacheKey := fmt.Sprintf("%s:%d", clientUUID, clientPort)
	delete(g.Mappings, cacheKey)
	return mapping.ID
}

func (g *PortMappingGroup) GetClientUUIDbyServerPort(serverPort int) string {
	return g.ServerPortMapping[serverPort]
}

func (g *PortMappingGroup) UpdateOnlineStatus(clientUUID string, clientPort int, online bool) {
	g.Locker.Lock()
	defer g.Locker.Unlock()

	cacheKey := fmt.Sprintf("%s:%d", clientUUID, clientPort)
	if mapping, ok := g.Mappings[cacheKey]; ok {
		mapping.Online = online
	}
}

func (g *PortMappingGroup) UpdateOnlineStatusByClientStatus(uuid string, online bool) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	for _, mapping := range g.Mappings {
		if mapping.Client.UUID == uuid {
			mapping.Online = online
		}
	}
}

// UpdateLastConnectionTime 更新指定映射的最后连接时间
func (g *PortMappingGroup) UpdateLastConnectionTime(clientUUID string, clientPort int) {
	g.Locker.Lock()
	defer g.Locker.Unlock()

	cacheKey := fmt.Sprintf("%s:%d", clientUUID, clientPort)
	if mapping, ok := g.Mappings[cacheKey]; ok {
		mapping.Connected = time.Now()
	}
}

// GetExpiredMappings 获取过期的端口映射
func (g *PortMappingGroup) GetExpiredMappings(expiration time.Duration) []*PortMapping {
	g.Locker.RLock()
	defer g.Locker.RUnlock()

	var expiredMappings []*PortMapping
	now := time.Now()

	for _, mapping := range g.Mappings {
		// 检查映射是否未启用或不在线，且最后连接时间超过过期时间
		if (!mapping.Enable || !mapping.Online) &&
			now.Sub(mapping.Connected) > expiration {
			expiredMappings = append(expiredMappings, mapping)
		}
	}

	return expiredMappings
}

func (m *URLMapping) IsBaseUrlExist(baseURL string) bool {
	m.RLock()
	defer m.RUnlock()
	_, ok := m.BaseURL[baseURL]
	return ok
}

func (m *URLMapping) DeleteBaseUrl(baseURL string) {
	m.Lock()
	defer m.Unlock()
	delete(m.BaseURL, baseURL)
}

func (m *URLMapping) MatchBaseURL(targetURL string) (string, int) {
	m.RLock()
	defer m.RUnlock()

	var longestMatch string
	var servicePort int
	maxLen := -1

	for baseURL, service := range m.BaseURL {
		// 确保baseURL是targetURL的前缀
		if strings.HasPrefix(targetURL, baseURL) {
			// 找出最长匹配
			if len(baseURL) > maxLen {
				maxLen = len(baseURL)
				longestMatch = baseURL
				servicePort = service.ServicePort
			}
		}
	}

	return longestMatch, servicePort
}

// URLMappingGroup 管理方法
func (g *URLMappingGroup) AddMapping(mapping *URLMapping) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	g.Mappings[mapping.URLPath] = mapping

	// 更新客户端路径映射
	clientUUID := mapping.Client.UUID
	if appPaths, exists := g.ClientPaths[clientUUID]; exists {
		// 检查路径是否已存在
		if paths, ok := appPaths[mapping.Name]; ok {
			for _, path := range paths {
				if path == mapping.URLPath {
					return // 路径已存在，不重复添加
				}
			}
			g.ClientPaths[clientUUID][mapping.Name] = append(paths, mapping.URLPath)
		}

	} else {
		g.ClientPaths[clientUUID] = make(map[string][]string)
		g.ClientPaths[clientUUID][mapping.Name] = []string{mapping.URLPath}
	}
}

func (g *URLMappingGroup) UpdateMapping(mapping *URLMapping) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	g.Mappings[mapping.URLPath] = mapping
	// todo 更新客户端路径映射
	if appPaths, exists := g.ClientPaths[mapping.Client.UUID]; exists {
		if paths, ok := appPaths[mapping.Name]; ok {
			for _, path := range paths {
				if path == mapping.URLPath {
					return
				}
			}
			g.ClientPaths[mapping.Client.UUID][mapping.Name] = append(paths, mapping.URLPath)

		}
	}
}

func (g *URLMappingGroup) GetMapping(urlPath string) *URLMapping {
	urlPath = strings.ToUpper(urlPath)
	g.Locker.RLock()
	defer g.Locker.RUnlock()
	if mapping, ok := g.Mappings[urlPath]; ok {
		return mapping
	}
	return nil
}

// 直接删除一个client下的所有urlPath
func (g *URLMappingGroup) DeleteMapping(urlPath string) {
	g.Locker.Lock()
	defer g.Locker.Unlock()

	if mapping, exists := g.Mappings[urlPath]; exists {
		clientUUID := mapping.Client.UUID
		delete(g.Mappings, urlPath)

		// 从客户端路径映射中删除
		if appPaths, exists := g.ClientPaths[clientUUID]; exists {
			if paths, ok := appPaths[mapping.Name]; ok {
				newPaths := make([]string, 0, len(paths)-1)
				for _, path := range paths {
					if path != urlPath {
						newPaths = append(newPaths, path)
					}
				}
				if len(newPaths) == 0 {
					delete(g.ClientPaths[clientUUID], mapping.Name)
				} else {
					g.ClientPaths[clientUUID][mapping.Name] = newPaths
				}
			}
		}
	}
}

// 找个函数存在问题，不能通过baseURL找到mapping的
func (g *URLMappingGroup) DeleteMappingBaseURL(baseURL string) {
	g.Locker.Lock()
	defer g.Locker.Unlock()

	if mapping, exists := g.Mappings[baseURL]; exists {
		mapping.DeleteBaseUrl(baseURL)
	}

}

func (g *URLMappingGroup) GetClientPaths(clientUUID string) []string {
	g.Locker.RLock()
	defer g.Locker.RUnlock()
	if paths, exists := g.ClientPaths[clientUUID]; exists {
		var result []string
		for _, paths := range paths {
			// 返回副本以避免并发修改
			result = append(result, paths...)
		}
		return result
	}
	return nil
}

func (g *URLMappingGroup) UpdateOnlineStatusByClientStatus(uuid string, online bool) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	for _, mapping := range g.Mappings {
		if mapping.Client.UUID == uuid {
			mapping.Online = online
		}
	}
}
